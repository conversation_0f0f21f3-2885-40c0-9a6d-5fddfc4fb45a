kotlin version: 2.0.21
error message: Incremental compilation failed: java.lang.AssertionError: data corruption detected:29,30
java.io.IOException: java.lang.AssertionError: data corruption detected:29,30
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.loadChunk(CompressedAppendableFile.java:195)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.access$100(CompressedAppendableFile.java:31)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile$FileChunkReadCache.get(CompressedAppendableFile.java:468)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile$SegmentedChunkInputStream.read(CompressedAppendableFile.java:523)
	at java.base/java.io.DataInputStream.readFully(Unknown Source)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMapValueStorage$ReaderOverCompressedFile.get(PersistentHashMapValueStorage.java:815)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMapValueStorage.readBytes(PersistentHashMapValueStorage.java:588)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.doGet(PersistentMapImpl.java:676)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.get(PersistentMapImpl.java:613)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMap.get(PersistentHashMap.java:196)
	at org.jetbrains.kotlin.incremental.storage.LazyStorage.get(LazyStorage.kt:76)
	at org.jetbrains.kotlin.incremental.storage.InMemoryStorage.get(InMemoryStorage.kt:68)
	at org.jetbrains.kotlin.incremental.storage.AppendableInMemoryStorage.get(InMemoryStorage.kt:155)
	at org.jetbrains.kotlin.incremental.storage.AppendableInMemoryStorage.get(InMemoryStorage.kt:147)
	at org.jetbrains.kotlin.incremental.storage.AppendableSetBasicMap.get(BasicMap.kt:137)
	at org.jetbrains.kotlin.incremental.TrackedLookupMap.get(LookupStorage.kt:308)
	at org.jetbrains.kotlin.incremental.LookupStorage.get(LookupStorage.kt:94)
	at org.jetbrains.kotlin.incremental.BuildUtilKt.mapLookupSymbolsToFiles(buildUtil.kt:221)
	at org.jetbrains.kotlin.incremental.BuildUtilKt.mapLookupSymbolsToFiles$default(buildUtil.kt:212)
	at org.jetbrains.kotlin.incremental.DirtyFilesContainer.addByDirtySymbols(DirtyFilesContainer.kt:37)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompileImpl(IncrementalJvmCompilerRunner.kt:251)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompile(IncrementalJvmCompilerRunner.kt:143)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompile(IncrementalJvmCompilerRunner.kt:73)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:225)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:675)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1660)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: java.lang.AssertionError: data corruption detected:29,30
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.loadChunk(CompressedAppendableFile.java:191)
	... 43 more


