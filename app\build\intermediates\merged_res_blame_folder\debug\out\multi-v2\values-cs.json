{"logs": [{"outputFile": "com.example.my_music_001.app-mergeDebugResources-48:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3dfcadad40a732775724e376d406a3c9\\transformed\\foundation-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8676,8762", "endColumns": "85,88", "endOffsets": "8757,8846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f812aff50628458f92d3d65ed6b25c25\\transformed\\material3-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4599,4691,4777,4890,4970,5056,5157,5260,5357,5458,5546,5652,5751,5854,5973,6053,6157", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4594,4686,4772,4885,4965,5051,5152,5255,5352,5453,5541,5647,5746,5849,5968,6048,6152,6247"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1470,1588,1705,1824,1942,2043,2138,2250,2384,2500,2639,2724,2824,2917,3014,3130,3252,3357,3490,3620,3762,3925,4053,4170,4294,4415,4506,4603,4723,4838,4936,5039,5147,5279,5420,5530,5629,5713,5807,5902,6014,6106,6192,6305,6385,6471,6572,6675,6772,6873,6961,7067,7166,7269,7388,7468,7572", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "1583,1700,1819,1937,2038,2133,2245,2379,2495,2634,2719,2819,2912,3009,3125,3247,3352,3485,3615,3757,3920,4048,4165,4289,4410,4501,4598,4718,4833,4931,5034,5142,5274,5415,5525,5624,5708,5802,5897,6009,6101,6187,6300,6380,6466,6567,6670,6767,6868,6956,7062,7161,7264,7383,7463,7567,7662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c207a12670627327d0104d8324c8bc3f\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,505,610,717,8308", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "198,300,401,500,605,712,831,8404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c02159d97b58df1ac6d5f03a0b5b0bd8\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,993,1079,1151,1229,1305,1380,1459,1527", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,988,1074,1146,1224,1300,1375,1454,1522,1642"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1012,1106,1208,1300,1378,7667,7758,7839,7921,8007,8079,8157,8233,8409,8488,8556", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,77,75,74,78,67,119", "endOffsets": "924,1007,1101,1203,1295,1373,1465,7753,7834,7916,8002,8074,8152,8228,8303,8483,8551,8671"}}]}]}