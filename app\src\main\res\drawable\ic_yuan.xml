<vector xmlns:android="http://schemas.android.com/apk/res/android" android:height="24dp" android:tint="#000000" android:viewportHeight="24" android:viewportWidth="24" android:width="24dp">
    <!-- 添加细的圆环 -->
    <path android:strokeColor="@android:color/white" android:strokeWidth="0.5" android:fillColor="@android:color/transparent" android:pathData="M12,3C7.03,3 3,7.03 3,12s4.03,9 9,9 9,-4.03 9,-9S16.97,3 12,3z"/>
    
    <!-- 向上移动并居中的三个小圆 -->
    <path android:fillColor="@android:color/white" android:pathData="M7.5,11c-1.65,0 -3,1.35 -3,3s1.35,3 3,3s3,-1.35 3,-3S9.15,11 7.5,11zM12,5c-1.65,0 -3,1.35 -3,3s1.35,3 3,3s3,-1.35 3,-3S13.65,5 12,5zM16.5,11c-1.65,0 -3,1.35 -3,3s1.35,3 3,3s3,-1.35 3,-3S18.15,11 16.5,11z"/>
</vector>
