<libraries>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29688485273a57a8a359b0103f74c33d\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29688485273a57a8a359b0103f74c33d\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96823d9ea864b7dfe54457f103aed646\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96823d9ea864b7dfe54457f103aed646\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d1fec13faa8a1c2911a525344e75f34\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d1fec13faa8a1c2911a525344e75f34\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5b639fd13d83c7bb394a7ee9df10b4c\transformed\media-1.7.0\jars\classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5b639fd13d83c7bb394a7ee9df10b4c\transformed\media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79be65f519aa7f6f13d4f2f44666772d\transformed\coil-compose-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79be65f519aa7f6f13d4f2f44666772d\transformed\coil-compose-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a12570c096ffb99db54a8463767fe5b\transformed\coil-compose-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a12570c096ffb99db54a8463767fe5b\transformed\coil-compose-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daf700347b29a0c70c470e3abdda46f2\transformed\coil-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daf700347b29a0c70c470e3abdda46f2\transformed\coil-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08ce5e86a08d5239cf80437cb3bf5295\transformed\coil-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08ce5e86a08d5239cf80437cb3bf5295\transformed\coil-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\266abb3ffc4f06d526508e9071221fee\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\266abb3ffc4f06d526508e9071221fee\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c31104634f16a847157f5fbe8dcdbb2\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c31104634f16a847157f5fbe8dcdbb2\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c7921188f4014cd23558ac4b01feb89\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c7921188f4014cd23558ac4b01feb89\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39bbce88f74484d5750c9f2d4bc42993\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39bbce88f74484d5750c9f2d4bc42993\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4820015212886665acacb9ccd2075993\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4820015212886665acacb9ccd2075993\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77a842be4e6a024381a567a3ad1dfb68\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77a842be4e6a024381a567a3ad1dfb68\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b57ffc025acb7e960dd010855e1c62ee\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b57ffc025acb7e960dd010855e1c62ee\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46855ebba53be26c69ceb3babfd0cdcf\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46855ebba53be26c69ceb3babfd0cdcf\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45e66a03cafc5012cb9c734381d0689\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45e66a03cafc5012cb9c734381d0689\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\187b766939fe6e8eab2b1ad478b2d649\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\187b766939fe6e8eab2b1ad478b2d649\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232f7da97919e638a3562a7bce31da31\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232f7da97919e638a3562a7bce31da31\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d0267b127563cdd54457bcd14468c1\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d0267b127563cdd54457bcd14468c1\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63c28fa7e4f6c13e7eecc7224d73e36e\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63c28fa7e4f6c13e7eecc7224d73e36e\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc026bb76e99bbb3a1041a14632c8ff\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc026bb76e99bbb3a1041a14632c8ff\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41718bf4de115c189013ca6b78994566\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41718bf4de115c189013ca6b78994566\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8ec20d6a47c246e733e7fc87f63baa5\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8ec20d6a47c246e733e7fc87f63baa5\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b906286dc5a691463ab47ffb08c6f491\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b906286dc5a691463ab47ffb08c6f491\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad7e14426b4936c1e639050ee6af23b\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad7e14426b4936c1e639050ee6af23b\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcaa712b5c282d461ebff2b2c649d7df\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcaa712b5c282d461ebff2b2c649d7df\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a5b3c47f27fa8930537910f5108e55\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a5b3c47f27fa8930537910f5108e55\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d9e74007e84fe0f4572d8ad2a7b3\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d9e74007e84fe0f4572d8ad2a7b3\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08e05a63d31d92a0defa13165da34b2a\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08e05a63d31d92a0defa13165da34b2a\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.11.0\87fa769912b1f738f3c2dd87e3bca4d1d7f0e666\logging-interceptor-4.11.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.11.0"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d541c873257cbe08947b80ed21069c1c\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d541c873257cbe08947b80ed21069c1c\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a040dbb65e57e26e7905ea8c1299f8c3\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a040dbb65e57e26e7905ea8c1299f8c3\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.documentfile:documentfile:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0be71ae125b087b238a4b0710460bde\transformed\documentfile-1.0.1\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0be71ae125b087b238a4b0710460bde\transformed\documentfile-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd751045bd3c7449a132a5c8e7a0f5d\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd751045bd3c7449a132a5c8e7a0f5d\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985c9a3f872c6ae74a1e5a18ad3365fa\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985c9a3f872c6ae74a1e5a18ad3365fa\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.0\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e9f852547f60b1c88824a9e88c46c0e\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e9f852547f60b1c88824a9e88c46c0e\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.accompanist:accompanist-drawablepainter:0.32.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f63dd6298cbd21802e17dc5c5b455ef\transformed\accompanist-drawablepainter-0.32.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-drawablepainter:0.32.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f63dd6298cbd21802e17dc5c5b455ef\transformed\accompanist-drawablepainter-0.32.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2912a3dcdc89386f62142819d4445\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2912a3dcdc89386f62142819d4445\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f13214e8c516f2a1222748d92392911\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f13214e8c516f2a1222748d92392911\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb377637f5c812b0be81702ac37c175\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb377637f5c812b0be81702ac37c175\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1b9660cc6bde413c42e44e7cc6d8adc\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1b9660cc6bde413c42e44e7cc6d8adc\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff72b114f5374f18d2d6436cd1cd9a9b\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff72b114f5374f18d2d6436cd1cd9a9b\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2c3a2a5ae3df01c692f61db55cde14\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2c3a2a5ae3df01c692f61db55cde14\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.3\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.3"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973d0f6da1224beea11673cbe6f948c6\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973d0f6da1224beea11673cbe6f948c6\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48cb23a03acfb9f5b9ec9a54c59e8db\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48cb23a03acfb9f5b9ec9a54c59e8db\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2840b2634eefce444aa554fac32de3f\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2840b2634eefce444aa554fac32de3f\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aca3b254ef5fa637668ebfc0eed36c4\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aca3b254ef5fa637668ebfc0eed36c4\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
