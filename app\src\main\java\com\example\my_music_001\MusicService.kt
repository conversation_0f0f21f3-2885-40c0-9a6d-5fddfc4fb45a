package com.example.my_music_001
//状态栏
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import kotlinx.coroutines.*

class MusicService : Service() {
    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "music_player_channel"
        const val ACTION_UPDATE_TIMER = "com.example.my_music_001.ACTION_UPDATE_TIMER"
        const val EXTRA_TIMER_REMAINING = "timer_remaining"
        const val EXTRA_TIMER_ACTIVE = "timer_active"
        const val EXTRA_TIMER_PLAYING = "timer_playing"
        const val EXTRA_TIMER_PLAY_MODE = "timer_play_mode"
    }

    private var wakeLock: PowerManager.WakeLock? = null
    private val serviceScope = CoroutineScope(Dispatchers.Default)
    private var timerJob: Job? = null
    
    // 倒计时状态
    private var timerRemainingMillis: Long = 0
    private var timerActive: Boolean = false
    private var timerPlaying: Boolean = false
    private var timerPlayMode: Int = 0

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        
        // 创建WakeLock，防止设备休眠
        val powerManager = getSystemService(POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "My_music_001:ServiceWakeLock"
        ).apply {
            setReferenceCounted(false)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 创建一个简单的通知来保持服务在前台运行
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle("音乐播放器")
            .setContentText("正在后台运行")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()

        // 启动前台服务
        startForeground(NOTIFICATION_ID, notification)
        
        // 处理倒计时更新意图
        if (intent?.action == ACTION_UPDATE_TIMER) {
            timerRemainingMillis = intent.getLongExtra(EXTRA_TIMER_REMAINING, 0)
            timerActive = intent.getBooleanExtra(EXTRA_TIMER_ACTIVE, false)
            timerPlaying = intent.getBooleanExtra(EXTRA_TIMER_PLAYING, false)
            timerPlayMode = intent.getIntExtra(EXTRA_TIMER_PLAY_MODE, 0)
            
            Log.d("MusicService", "收到倒计时更新: 剩余=${timerRemainingMillis/1000}秒, 活跃=$timerActive, 播放=$timerPlaying")
            
            // 如果倒计时活跃且正在播放且剩余时间大于0，确保WakeLock被获取
            if (timerActive && timerPlaying && timerRemainingMillis > 0) {
                if (wakeLock?.isHeld != true) {
                    wakeLock?.acquire(30*60*1000L) // 最多持有30分钟
                    Log.d("MusicService", "获取WakeLock以保持倒计时运行")
                }
                
                // 通知MainActivity更新UI
                val updateIntent = Intent("com.example.my_music_001.TIMER_TICK")
                updateIntent.putExtra("timer_remaining", timerRemainingMillis)
                sendBroadcast(updateIntent)
                
                // 如果剩余时间很短（小于5分钟），增加更新频率
                if (timerRemainingMillis < 5 * 60 * 1000) {
                    // 启动一个单次任务，确保在短时间内再次发送更新
                    Handler(Looper.getMainLooper()).postDelayed({
                        // 只有当服务仍在运行时才发送更新
                        try {
                            val refreshIntent = Intent("com.example.my_music_001.TIMER_TICK")
                            refreshIntent.putExtra("timer_remaining", timerRemainingMillis)
                            sendBroadcast(refreshIntent)
                            Log.d("MusicService", "发送额外的倒计时更新: ${timerRemainingMillis/1000}秒")
                        } catch (e: Exception) {
                            Log.e("MusicService", "发送额外更新失败: ${e.message}")
                        }
                    }, 15000) // 15秒后再次更新
                }
                
            } else if (timerRemainingMillis <= 0 || !timerActive) {
                // 倒计时已结束或不再活跃，确保释放WakeLock
                if (wakeLock?.isHeld == true) {
                    wakeLock?.release()
                    Log.d("MusicService", "倒计时结束或不再活跃，释放WakeLock")
                }
                
                // 发送一个最终的UI更新，确保显示为0
                if (timerRemainingMillis <= 0) {
                    // 清除倒计时状态
                    timerRemainingMillis = 0
                    timerActive = false
                    timerPlaying = false
                    
                    // 发送最终更新
                    val finalUpdateIntent = Intent("com.example.my_music_001.TIMER_TICK")
                    finalUpdateIntent.putExtra("timer_remaining", 0L)
                    finalUpdateIntent.putExtra("timer_ended", true) // 添加标记表示倒计时已结束
                    sendBroadcast(finalUpdateIntent)
                    
                    // 确保广播能被接收到，重复发送几次
                    for (i in 1..3) {
                        Handler(Looper.getMainLooper()).postDelayed({
                            try {
                                val repeatIntent = Intent("com.example.my_music_001.TIMER_TICK")
                                repeatIntent.putExtra("timer_remaining", 0L)
                                repeatIntent.putExtra("timer_ended", true)
                                sendBroadcast(repeatIntent)
                                Log.d("MusicService", "重复发送倒计时结束广播 #$i")
                            } catch (e: Exception) {
                                Log.e("MusicService", "重复发送广播失败: ${e.message}")
                            }
                        }, i * 1000L) // 间隔1秒、2秒、3秒发送
                    }
                    
                    Log.d("MusicService", "倒计时已结束，发送最终的0秒更新并清除状态")
                    
                    // 更新前台服务通知，确保不再显示倒计时信息
                    try {
                        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                            .setSmallIcon(R.mipmap.ic_launcher)
                            .setContentTitle("音乐播放器")
                            .setContentText("正在后台运行") // 不再显示倒计时信息
                            .setPriority(NotificationCompat.PRIORITY_LOW)
                            .setOngoing(true)
                            .build()
                        
                        startForeground(NOTIFICATION_ID, notification)
                        Log.d("MusicService", "已更新服务通知，移除倒计时信息")
                    } catch (e: Exception) {
                        Log.e("MusicService", "更新通知失败: ${e.message}")
                    }
                }
            } else {
                // 其他情况也确保WakeLock状态正确
                if (timerPlaying) {
                    // 如果倒计时在播放，但WakeLock未持有，获取WakeLock
                    if (wakeLock?.isHeld != true) {
                        wakeLock?.acquire(30*60*1000L) // 最多持有30分钟
                        Log.d("MusicService", "其他情况下获取WakeLock")
                    }
                } else {
                    // 如果倒计时不在播放，但WakeLock持有，释放WakeLock
                    if (wakeLock?.isHeld == true) {
                        wakeLock?.release()
                        Log.d("MusicService", "其他情况下释放WakeLock")
                    }
                }
                
                // 通知MainActivity更新UI
                val updateIntent = Intent("com.example.my_music_001.TIMER_TICK")
                updateIntent.putExtra("timer_remaining", timerRemainingMillis)
                sendBroadcast(updateIntent)
            }
        }
        
        return START_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "音乐播放器",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "显示音乐播放状态"
                setShowBadge(false)
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager?.createNotificationChannel(channel)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 取消所有协程
        serviceScope.cancel()
        
        // 释放WakeLock
        if (wakeLock?.isHeld == true) {
            wakeLock?.release()
        }
        
        stopForeground(true)
    }
}
