{"logs": [{"outputFile": "com.example.my_music_001.app-mergeDebugResources-47:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\52d0267b127563cdd54457bcd14468c1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "5007", "endColumns": "65", "endOffsets": "5068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\29688485273a57a8a359b0103f74c33d\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "88,106", "startColumns": "4,4", "startOffsets": "5432,6376", "endColumns": "41,59", "endOffsets": "5469,6431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b906286dc5a691463ab47ffb08c6f491\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6333", "endColumns": "42", "endOffsets": "6371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ebc026bb76e99bbb3a1041a14632c8ff\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,79,81,82,109,122,123,124,125,126,127,128,190,192,195,196,197,198,199,200,202,203,204,207,223,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2860,2919,2978,3038,3098,3158,3218,3278,3338,3398,3458,3518,3578,3637,3697,3757,3817,3877,3937,3997,4057,4117,4177,4237,4296,4356,4416,4475,4534,4593,4652,4711,4770,4894,4952,5073,5124,6540,7433,7498,7552,7618,7719,7777,7829,12330,12429,12560,12610,12664,12710,12756,12798,12909,12956,12992,13193,14173,14284", "endLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,78,79,81,82,109,122,123,124,125,126,127,128,190,192,195,196,197,198,199,200,202,203,204,209,225,229", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2914,2973,3033,3093,3153,3213,3273,3333,3393,3453,3513,3573,3632,3692,3752,3812,3872,3932,3992,4052,4112,4172,4232,4291,4351,4411,4470,4529,4588,4647,4706,4765,4839,4947,5002,5119,5174,6588,7493,7547,7613,7714,7772,7824,7884,12387,12478,12605,12659,12705,12751,12793,12833,12951,12987,13077,13300,14279,14474"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\My_music_001\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2,31", "startColumns": "4,4", "startOffsets": "55,1286", "endLines": "28,35", "endColumns": "12,12", "endOffsets": "1247,1589"}, "to": {"startLines": "246,273", "startColumns": "4,4", "startOffsets": "15619,16682", "endLines": "272,277", "endColumns": "12,12", "endOffsets": "16677,16985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\08ce5e86a08d5239cf80437cb3bf5295\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "4844", "endColumns": "49", "endOffsets": "4889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\985c9a3f872c6ae74a1e5a18ad3365fa\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "6436", "endColumns": "53", "endOffsets": "6485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b57ffc025acb7e960dd010855e1c62ee\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "205,206", "startColumns": "4,4", "startOffsets": "13082,13138", "endColumns": "55,54", "endOffsets": "13133,13188"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\My_music_001\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,12,13,14,16,17,18", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,835,882,929,1049,1094,1139", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,877,924,971,1089,1134,1176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8ad7e14426b4936c1e639050ee6af23b\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6490", "endColumns": "49", "endOffsets": "6535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e5b639fd13d83c7bb394a7ee9df10b4c\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "10,11,15,86,110,235,237,238,243,245", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "675,764,976,5303,6593,14797,14973,15095,15357,15552", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "759,830,1044,5360,6648,14858,15090,15151,15418,15614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e2c3a2a5ae3df01c692f61db55cde14\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "83,87", "startColumns": "4,4", "startOffsets": "5179,5365", "endColumns": "53,66", "endOffsets": "5228,5427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0e26402159bb58fcf9c37f176465b2c\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6796", "endColumns": "82", "endOffsets": "6874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\266abb3ffc4f06d526508e9071221fee\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "111,129,130,131,132,133,134,135,136,137,138,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,210,220", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6653,7889,7977,8063,8144,8228,8297,8362,8445,8551,8637,8757,8811,8880,8941,9010,9099,9194,9268,9365,9458,9556,9705,9796,9884,9980,10078,10142,10210,10297,10391,10458,10530,10602,10703,10812,10888,10957,11005,11071,11135,11209,11266,11323,11395,11445,11499,11570,11641,11711,11780,11838,11914,11985,12059,12145,12195,12265,13305,14020", "endLines": "111,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,219,222", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "6721,7972,8058,8139,8223,8292,8357,8440,8546,8632,8752,8806,8875,8936,9005,9094,9189,9263,9360,9453,9551,9700,9791,9879,9975,10073,10137,10205,10292,10386,10453,10525,10597,10698,10807,10883,10952,11000,11066,11130,11204,11261,11318,11390,11440,11494,11565,11636,11706,11775,11833,11909,11980,12054,12140,12190,12260,12325,14015,14168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7cb377637f5c812b0be81702ac37c175\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "280,296,302,421,437", "startColumns": "4,4,4,4,4", "startOffsets": "17129,17554,17732,21781,22192", "endLines": "295,301,311,436,440", "endColumns": "24,24,24,24,24", "endOffsets": "17549,17727,18011,22187,22314"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\My_music_001\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,4,3,2", "startColumns": "4,4,4,4", "startOffsets": "16,136,100,59", "endColumns": "42,36,35,40", "endOffsets": "54,168,131,95"}, "to": {"startLines": "114,191,193,194", "startColumns": "4,4,4,4", "startOffsets": "6879,12392,12483,12519", "endColumns": "42,36,35,40", "endOffsets": "6917,12424,12514,12555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d791d414c3343198b7bbd81c2864601a\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,8,9,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,112,115,116,117,118,119,120,121,201,230,231,236,239,244,278,279,312,318,328,361,382,415", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,543,612,1181,1251,1319,1391,1461,1522,1596,1669,1730,1791,1853,1917,1979,2040,2108,2208,2268,2334,2407,2476,2533,2585,2647,2719,2795,5233,5268,5474,5529,5592,5647,5705,5763,5824,5887,5944,5995,6045,6106,6163,6229,6263,6298,6726,6922,6989,7061,7130,7199,7273,7345,12838,14479,14596,14863,15156,15423,16990,17062,18016,18219,18520,20251,20932,21614", "endLines": "2,3,4,6,7,8,9,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,112,115,116,117,118,119,120,121,201,230,234,236,242,244,278,279,317,327,360,381,414,420", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,607,670,1246,1314,1386,1456,1517,1591,1664,1725,1786,1848,1912,1974,2035,2103,2203,2263,2329,2402,2471,2528,2580,2642,2714,2790,2855,5263,5298,5524,5587,5642,5700,5758,5819,5882,5939,5990,6040,6101,6158,6224,6258,6293,6328,6791,6984,7056,7125,7194,7268,7340,7428,12904,14591,14792,14968,15352,15547,17057,17124,18214,18515,20246,20927,21609,21776"}}]}]}