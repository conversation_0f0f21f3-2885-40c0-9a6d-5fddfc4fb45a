package com.example.my_music_001

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout

/**
 * 空白弹窗类
 * 可以通过继承此类来创建自定义弹窗
 */
class EmptyDialog(context: Context) : Dialog(context) {
    
    init {
        // 设置无标题
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        // 设置背景透明
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        // 创建一个空的LinearLayout作为内容
        val layout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.CENTER
        }
        setContentView(layout)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置弹窗位置和大小
        window?.apply {
            setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            setGravity(Gravity.CENTER)
        }
    }
    
    /**
     * 显示弹窗的便捷方法
     */
    fun showDialog() {
        if (!isShowing) {
            show()
        }
    }
}

