{"logs": [{"outputFile": "com.example.my_music_001.app-mergeDebugResources-48:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c207a12670627327d0104d8324c8bc3f\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,8479", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,8575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c02159d97b58df1ac6d5f03a0b5b0bd8\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,949,1033,1131,1236,1331,1408,7830,7917,8001,8087,8175,8250,8327,8404,8580,8660,8743", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "944,1028,1126,1231,1326,1403,1494,7912,7996,8082,8170,8245,8322,8399,8474,8655,8738,8860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3dfcadad40a732775724e376d406a3c9\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8865,8953", "endColumns": "87,87", "endOffsets": "8948,9036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f812aff50628458f92d3d65ed6b25c25\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4709,4797,4887,4998,5078,5165,5265,5374,5470,5569,5657,5768,5864,5964,6102,6186,6289", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4704,4792,4882,4993,5073,5160,5260,5369,5465,5564,5652,5763,5859,5959,6097,6181,6284,6381"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1499,1618,1738,1855,1973,2074,2168,2279,2411,2527,2671,2755,2854,2950,3049,3174,3292,3396,3535,3670,3809,4005,4135,4253,4379,4506,4603,4704,4826,4955,5053,5156,5263,5401,5549,5658,5762,5846,5942,6038,6153,6241,6331,6442,6522,6609,6709,6818,6914,7013,7101,7212,7308,7408,7546,7630,7733", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "1613,1733,1850,1968,2069,2163,2274,2406,2522,2666,2750,2849,2945,3044,3169,3287,3391,3530,3665,3804,4000,4130,4248,4374,4501,4598,4699,4821,4950,5048,5151,5258,5396,5544,5653,5757,5841,5937,6033,6148,6236,6326,6437,6517,6604,6704,6813,6909,7008,7096,7207,7303,7403,7541,7625,7728,7825"}}]}]}