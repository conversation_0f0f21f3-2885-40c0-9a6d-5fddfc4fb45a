<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.My_music_001" parent="android:Theme.Material.Light.NoActionBar">
        <!-- 设置窗口背景为黑色半透明覆盖层 -->
        <item name="android:windowBackground">@drawable/black_overlay</item>
        
        <!-- 状态栏透明 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        
        <!-- 允许内容延伸到状态栏下方 -->
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">false</item>
        
        <!-- 适配全面屏和刘海屏 -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        
        <!-- 导航栏颜色 -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
        
        <!-- 确保状态栏透明 -->
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="android:enforceStatusBarContrast">false</item>
        
        <!-- 允许对话框背景效果，但由应用自己控制 -->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0</item>
    </style>
    
    <!-- 自定义对话框主题，禁用背景变暗 -->
    <style name="TransparentDialogTheme" parent="@android:style/Theme.Material.Dialog.Alert">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
    </style>
</resources>