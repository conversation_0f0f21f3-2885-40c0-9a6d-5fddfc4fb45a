package com.example.my_music_001

import androidx.compose.ui.graphics.Color

val predefinedGradients = listOf(
    // 深黑色系
    Triple(Color(0xFF2E2E2E), Color(0xFF252525), Color(0xFF1A1A1A)),

    // 深蓝色系
    Triple(Color(0xFF1E3264), Color(0xFF162449), Color(0xFF0E1530)),

    // 深紫色系
    Triple(Color(0xFF4A2563), Color(0xFF371B49), Color(0xFF251033)),

    // 深红色系
    Triple(Color(0xFF5E2428), Color(0xFF45191C), Color(0xFF2E1012)),

    // 深绿色系
    Triple(Color(0xFF1F3B2C), Color(0xFF172B21), Color(0xFF0F1C15)),

    // 深橙色系
    Triple(Color(0xFF5E3311), Color(0xFF452408), Color(0xFF2E1805)),

    // 暗青色系
    Triple(Color(0xFF18454B), Color(0xFF123338), Color(0xFF0C2025)),

    // 暗棕灰色系
    Triple(Color(0xFF383330), Color(0xFF282624), Color(0xFF1C1A18)),

    // 暗紫灰色系
    Triple(Color(0xFF372E3B), Color(0xFF28222B), Color(0xFF1B171D)),

    // 暗深玫瑰红
    Triple(Color(0xFF4A1A32), Color(0xFF371525), Color(0xFF25101A)),

    // 深梨绿色系
    Triple(Color(0xFF3F5E20), Color(0xFF2F4518), Color(0xFF202F10)),

    // 深孔雀绿色系
    Triple(Color(0xFF1A4D3B), Color(0xFF133A2C), Color(0xFF0D271E)),

    // 深紫蓝色系
    Triple(Color(0xFF293C78), Color(0xFF1F2D5A), Color(0xFF141E3C)),

    // 深青紫色系
    Triple(Color(0xFF2D3A6E), Color(0xFF222C53), Color(0xFF171E38)),

    // 灰蓝色系
    Triple(Color(0xFF394A59), Color(0xFF2B3843), Color(0xFF1D262E)),

    // 深青绿色系
    Triple(Color(0xFF1A5259), Color(0xFF133E43), Color(0xFF0D2A2D)),

    // 深海军蓝色系
    Triple(Color(0xFF114466), Color(0xFF0D334C), Color(0xFF092233)),

    // 深酒红色系
    Triple(Color(0xFF631E36), Color(0xFF4A1728), Color(0xFF310F1B)),

    // 深绿松石色系
    Triple(Color(0xFF37595C), Color(0xFF284244), Color(0xFF1A2B2C)),

    // 深咖啡色系
    Triple(Color(0xFF4E3629), Color(0xFF3A281F), Color(0xFF271B14)),

    // 深墨紫色系
    Triple(Color(0xFF322742), Color(0xFF251D31), Color(0xFF181221)),

    // 深大理石蓝色系
    Triple(Color(0xFF2B4C5E), Color(0xFF203946), Color(0xFF15252F)),

    // 深川紫色系
    Triple(Color(0xFF493353), Color(0xFF36253E), Color(0xFF231829)),

    // 复古棕褐色系
    Triple(Color(0xFF8C6D4F), Color(0xFF6B543E), Color(0xFF4A3A2A)),

    // 复古薄荷绿
    Triple(Color(0xFF7BCFA6), Color(0xFF5FA080), Color(0xFF436F59)),

    // 暗酒红色系
    Triple(Color(0xFF7D3C3C), Color(0xFF5E2D2D), Color(0xFF3F1F1F)),

    // 宝石蓝色系
    Triple(Color(0xFF1A7BBE), Color(0xFF145F91), Color(0xFF0E4265)),

    // 金属银色系
    Triple(Color(0xFFB8B8B8), Color(0xFF8A8A8A), Color(0xFF5D5D5D)),

    // 金属金色系
    Triple(Color(0xFFD4AF37), Color(0xFFA0842A), Color(0xFF6D591C)),

    // 金属铜色系
    Triple(Color(0xFFB87333), Color(0xFF8A5727), Color(0xFF5D3A1A)),

    // 深海蓝色系
    Triple(Color(0xFF05445E), Color(0xFF043347), Color(0xFF022330)),

    // 珊瑚橙色系
    Triple(Color(0xFFFF7F50), Color(0xFFCC663F), Color(0xFF99492F)),

    // 森林绿色系
    Triple(Color(0xFF228B22), Color(0xFF1A6D1A), Color(0xFF124D12)),

    // 晚霞粉色系
    Triple(Color(0xFFFFC5BF), Color(0xFFCC9E99), Color(0xFF996F6A)),

    // 深松绿色系
    Triple(Color(0xFF2F4F4F), Color(0xFF243C3C), Color(0xFF192929)),

    // 烟雾灰色系
    Triple(Color(0xFF848884), Color(0xFF656A65), Color(0xFF464B46)),

    // 以下是逆序的颜色列表

    // 烟雾灰色系（逆序）
    Triple(Color(0xFF464B46), Color(0xFF656A65), Color(0xFF848884)),

    // 深松绿色系（逆序）
    Triple(Color(0xFF192929), Color(0xFF243C3C), Color(0xFF2F4F4F)),

    // 晚霜粉色系（逆序）
    Triple(Color(0xFF996F6A), Color(0xFFCC9E99), Color(0xFFFFC5BF)),

    // 森林绿色系（逆序）
    Triple(Color(0xFF124D12), Color(0xFF1A6D1A), Color(0xFF228B22)),

    // 珊瑚橙色系（逆序）
    Triple(Color(0xFF99492F), Color(0xFFCC663F), Color(0xFFFF7F50)),

    // 深海蓝色系（逆序）
    Triple(Color(0xFF022330), Color(0xFF043347), Color(0xFF05445E)),

    // 金属铜色系（逆序）
    Triple(Color(0xFF5D3A1A), Color(0xFF8A5727), Color(0xFFB87333)),

    // 金属金色系（逆序）
    Triple(Color(0xFF6D591C), Color(0xFFA0842A), Color(0xFFD4AF37)),

    // 金属银色系（逆序）
    Triple(Color(0xFF5D5D5D), Color(0xFF8A8A8A), Color(0xFFB8B8B8)),

    // 宝石蓝色系（逆序）
    Triple(Color(0xFF0E4265), Color(0xFF145F91), Color(0xFF1A7BBE)),

    // 暗酒红色系（逆序）
    Triple(Color(0xFF3F1F1F), Color(0xFF5E2D2D), Color(0xFF7D3C3C)),

    // 复古薄荷绿（逆序）
    Triple(Color(0xFF436F59), Color(0xFF5FA080), Color(0xFF7BCFA6)),

    // 复古棕褐色系（逆序）
    Triple(Color(0xFF4A3A2A), Color(0xFF6B543E), Color(0xFF8C6D4F)),

    // 深川紫色系（逆序）
    Triple(Color(0xFF231829), Color(0xFF36253E), Color(0xFF493353)),

    // 深大理石蓝色系（逆序）
    Triple(Color(0xFF15252F), Color(0xFF203946), Color(0xFF2B4C5E)),

    // 深墨紫色系（逆序）
    Triple(Color(0xFF181221), Color(0xFF251D31), Color(0xFF322742)),

    // 深咖啡色系（逆序）
    Triple(Color(0xFF271B14), Color(0xFF3A281F), Color(0xFF4E3629)),

    // 深绿松石色系（逆序）
    Triple(Color(0xFF1A2B2C), Color(0xFF284244), Color(0xFF37595C)),

    // 深酒红色系（逆序）
    Triple(Color(0xFF310F1B), Color(0xFF4A1728), Color(0xFF631E36)),

    // 深海军蓝色系（逆序）
    Triple(Color(0xFF092233), Color(0xFF0D334C), Color(0xFF114466)),

    // 深青绿色系（逆序）
    Triple(Color(0xFF0D2A2D), Color(0xFF133E43), Color(0xFF1A5259)),

    // 灰蓝色系（逆序）
    Triple(Color(0xFF1D262E), Color(0xFF2B3843), Color(0xFF394A59)),

    // 深青紫色系（逆序）
    Triple(Color(0xFF171E38), Color(0xFF222C53), Color(0xFF2D3A6E)),

    // 深紫蓝色系（逆序）
    Triple(Color(0xFF141E3C), Color(0xFF1F2D5A), Color(0xFF293C78)),

    // 深孔雀绿色系（逆序）
    Triple(Color(0xFF0D271E), Color(0xFF133A2C), Color(0xFF1A4D3B)),

    // 深梨绿色系（逆序）
    Triple(Color(0xFF202F10), Color(0xFF2F4518), Color(0xFF3F5E20)),

    // 暗深玫瑰红（逆序）
    Triple(Color(0xFF25101A), Color(0xFF371525), Color(0xFF4A1A32)),

    // 暗紫灰色系（逆序）
    Triple(Color(0xFF1B171D), Color(0xFF28222B), Color(0xFF372E3B)),

    // 暗棕灰色系（逆序）
    Triple(Color(0xFF1C1A18), Color(0xFF282624), Color(0xFF383330)),

    // 暗青色系（逆序）
    Triple(Color(0xFF0C2025), Color(0xFF123338), Color(0xFF18454B)),

    // 深橙色系（逆序）
    Triple(Color(0xFF2E1805), Color(0xFF452408), Color(0xFF5E3311)),

    // 深绿色系（逆序）
    Triple(Color(0xFF0F1C15), Color(0xFF172B21), Color(0xFF1F3B2C)),

    // 深红色系（逆序）
    Triple(Color(0xFF2E1012), Color(0xFF45191C), Color(0xFF5E2428)),

    // 深紫色系（逆序）
    Triple(Color(0xFF251033), Color(0xFF371B49), Color(0xFF4A2563)),

    // 深蓝色系（逆序）
    Triple(Color(0xFF0E1530), Color(0xFF162449), Color(0xFF1E3264)),

    // 海洋湿地蓝
    Triple(Color(0xFF00838F), Color(0xFF006064), Color(0xFF004D40)),


    // 秋天棕
    Triple(Color(0xFF7A5547), Color(0xFF5E4136), Color(0xFF4F342D)),

    // 深空蓝
    Triple(Color(0xFF0D48A2), Color(0xFF0A377D), Color(0xFF072759)),

    // 深海绿
    Triple(Color(0xFF004E41), Color(0xFF00362D), Color(0xFF001F1A)),

    // 复古蓝
    Triple(Color(0xFF617E8C), Color(0xFF465B65), Color(0xFF384850)),

    // 海洋湿地蓝（逆序）
    Triple(Color(0xFF004D40), Color(0xFF006064), Color(0xFF00838F)),



    // 秋天棕（逆序）
    Triple(Color(0xFF4F342D), Color(0xFF5E4136), Color(0xFF7A5547)),

    // 深空蓝（逆序）
    Triple(Color(0xFF072658), Color(0xFF0A367C), Color(0xFF0D47A1)),

    // 深海绿（逆序）
    Triple(Color(0xFF001E19), Color(0xFF00352C), Color(0xFF004D40)),

    // 复古蓝（逆序）
    Triple(Color(0xFF37474F), Color(0xFF455A64), Color(0xFF607D8B)),

    // 宇宙蓝
    Triple(Color(0xFF3949AB), Color(0xFF303F9F), Color(0xFF283593)),



    // 宇宙蓝（逆序）
    Triple(Color(0xFF283593), Color(0xFF303F9F), Color(0xFF3949AB)),

    // 浅玉蓝（逆序）
    Triple(Color(0xFF00838F), Color(0xFF00ACC1), Color(0xFF26C6DA)),



    // 深玉青
    Triple(Color(0xFF00695D), Color(0xFF004D41), Color(0xFF00352D)),

    // 茶色系
    Triple(Color(0xFF7A5547), Color(0xFF5E4136), Color(0xFF3F2824)),

    // 暗夜蓝
    Triple(Color(0xFF1B247F), Color(0xFF131959), Color(0xFF0B0F33)),

    // 暗夜红
    Triple(Color(0xFFB81D1D), Color(0xFF831515), Color(0xFF4E0D0D)),

    // 暗夜紫
    Triple(Color(0xFF4B158D), Color(0xFF361065), Color(0xFF210A3D)),

    // 淡灰蓝
    Triple(Color(0xFFD0D9DD), Color(0xFFB1BFC6), Color(0xFF91A5AF)),

    // 金属灰
    Triple(Color(0xFF9F9F9F), Color(0xFF767676), Color(0xFF626262)),

    // 茶色系（逆序）
    Triple(Color(0xFF3E2723), Color(0xFF5D4037), Color(0xFF795548)),

    // 暗夜蓝（逆序）
    Triple(Color(0xFF0A0E32), Color(0xFF121858), Color(0xFF1A237E)),

    // 暗夜红（逆序）
    Triple(Color(0xFF4D0C0C), Color(0xFF821414), Color(0xFFB71C1C)),

    // 暗夜紫（逆序）
    Triple(Color(0xFF20093C), Color(0xFF350F64), Color(0xFF4A148C)),

    // 淡灰蓝（逆序）
    Triple(Color(0xFF90A4AE), Color(0xFFB0BEC5), Color(0xFFCFD8DC)),

    // 金属灰（逆序）
    Triple(Color(0xFF616161), Color(0xFF757575), Color(0xFF9E9E9E)),

    // 金属蓝（逆序）
    Triple(Color(0xFF448AFF), Color(0xFF2979FF), Color(0xFF2962FF)),


    // 水晶青
    Triple(Color(0xFF019789), Color(0xFF01887C), Color(0xFF01776C)),

    // 水晶深蓝
    Triple(Color(0xFF4052B6), Color(0xFF3A4AAC), Color(0xFF3140A0)),


    // 水晶青（逆序）
    Triple(Color(0xFF01776C), Color(0xFF01887C), Color(0xFF019789)),

    // 水晶深蓝（逆序）
    Triple(Color(0xFF3140A0), Color(0xFF3A4AAC), Color(0xFF4052B6)),
    
    // 墨玉黑
    Triple(Color(0xFF1C1C1C), Color(0xFF121212), Color(0xFF080808)),
    
    // 乌木棕
    Triple(Color(0xFF3E2723), Color(0xFF2D1E1A), Color(0xFF1C1411)),
    
    // 暗沉紫
    Triple(Color(0xFF37274D), Color(0xFF281C39), Color(0xFF191225)),
    
    // 墨绛红
    Triple(Color(0xFF4A2B2B), Color(0xFF361F1F), Color(0xFF231414)),
    
    // 幽暗绿
    Triple(Color(0xFF1B3B29), Color(0xFF142B1E), Color(0xFF0D1C13)),
    
    // 深邃蓝
    Triple(Color(0xFF0D2B4A), Color(0xFF091F36), Color(0xFF051422)),
    
    // 炭灰色
    Triple(Color(0xFF333333), Color(0xFF262626), Color(0xFF1A1A1A)),
    
    // 深墨绿
    Triple(Color(0xFF184230), Color(0xFF123124), Color(0xFF0C2018)),
    
    // 暗褐棕
    Triple(Color(0xFF432818), Color(0xFF311D12), Color(0xFF20130C)),
    
    // 炭黑蓝
    Triple(Color(0xFF101820), Color(0xFF0C1218), Color(0xFF080C10)),
    
    // 暗琥珀
    Triple(Color(0xFF633517), Color(0xFF492710), Color(0xFF301A0A)),
    
    // 浓郁紫
    Triple(Color(0xFF2A1A42), Color(0xFF1F1431), Color(0xFF140D21)),
    
    // 沉沦红
    Triple(Color(0xFF5C1A1A), Color(0xFF451313), Color(0xFF2E0D0D)),
    
    // 幽谷绿
    Triple(Color(0xFF224134), Color(0xFF193026), Color(0xFF102019)),
    
    // 黑曜石
    Triple(Color(0xFF1A1A2E), Color(0xFF131322), Color(0xFF0D0D16)),
    
    // 暗铜棕
    Triple(Color(0xFF593C28), Color(0xFF422C1E), Color(0xFF2C1D14)),
    
    // 幽冥紫
    Triple(Color(0xFF392B4D), Color(0xFF2A2039), Color(0xFF1C1525)),
    
    // 深烟灰
    Triple(Color(0xFF343434), Color(0xFF272727), Color(0xFF1A1A1A)),
    
    // 暗赭石
    Triple(Color(0xFF5E3023), Color(0xFF46241A), Color(0xFF2F1812)),
    
    // 青黛蓝
    Triple(Color(0xFF13334C), Color(0xFF0E2639), Color(0xFF091925)),
    
    // 新增十种深红色系 - 中文备注
    
    // 酒石红
    Triple(Color(0xFF6D1A1D), Color(0xFF551316), Color(0xFF3D0E10)),
    
    // 胭脂红
    Triple(Color(0xFF8C2D31), Color(0xFF6E2327), Color(0xFF50191D)),
    
    // 枣泥红
    Triple(Color(0xFF621212), Color(0xFF4B0E0E), Color(0xFF340A0A)),
    
    // 暗樱桃红
    Triple(Color(0xFF7A1E24), Color(0xFF5D171C), Color(0xFF401014)),
    
    // 玛瑙红
    Triple(Color(0xFF5D2129), Color(0xFF471920), Color(0xFF311117)),
    
    // 深铜锈红
    Triple(Color(0xFF5F2D22), Color(0xFF48221A), Color(0xFF311712)),
    
    // 石榴红
    Triple(Color(0xFF7F1A30), Color(0xFF611425), Color(0xFF430E1A)),
    
    // 醇酱红
    Triple(Color(0xFF5C2835), Color(0xFF461F28), Color(0xFF30151C)),
    
    // 火山赤红
    Triple(Color(0xFF8B2E25), Color(0xFF6A231C), Color(0xFF491813)),
    
    // 深莓红
    Triple(Color(0xFF5A1F2D), Color(0xFF441723), Color(0xFF2E1018)),
    
    // 新增十种深蓝色系 - 中文备注
    
    // 幽海蓝
    Triple(Color(0xFF0A3B5C), Color(0xFF082C46), Color(0xFF061E2F)),
    
    // 暗靛青
    Triple(Color(0xFF1F3F66), Color(0xFF172F4D), Color(0xFF0F1F33)),
    
    // 夜空蓝
    Triple(Color(0xFF0C223F), Color(0xFF091A30), Color(0xFF061121)),
    
    // 玄青蓝
    Triple(Color(0xFF173A5A), Color(0xFF112B44), Color(0xFF0B1D2E)),
    
    // 深墨蓝
    Triple(Color(0xFF0E1C36), Color(0xFF0A1528), Color(0xFF070E1A)),
    
    // 鸦青蓝
    Triple(Color(0xFF1E354D), Color(0xFF17283A), Color(0xFF0F1B27)),
    
    // 藏青蓝
    Triple(Color(0xFF142B43), Color(0xFF0F2033), Color(0xFF0A1522)),
    
    // 暗宝石蓝
    Triple(Color(0xFF0D446B), Color(0xFF0A3352), Color(0xFF072239)),
    
    // 深海蓝墨
    Triple(Color(0xFF0B304E), Color(0xFF08243B), Color(0xFF061828)),
    
    // 潜渊蓝
    Triple(Color(0xFF122F4C), Color(0xFF0D2339), Color(0xFF091726))
)