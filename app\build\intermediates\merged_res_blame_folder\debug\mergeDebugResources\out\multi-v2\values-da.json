{"logs": [{"outputFile": "com.example.my_music_001.app-mergeDebugResources-47:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d791d414c3343198b7bbd81c2864601a\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,400,498,605,714,8216", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "196,298,395,493,600,709,827,8312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\266abb3ffc4f06d526508e9071221fee\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4660,4745,4846,4926,5010,5111,5210,5305,5405,5492,5597,5699,5804,5921,6001,6103", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4655,4740,4841,4921,5005,5106,5205,5300,5400,5487,5592,5694,5799,5916,5996,6098,6197"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1446,1562,1675,1782,1896,1996,2091,2203,2347,2469,2618,2702,2802,2891,2985,3099,3217,3322,3447,3567,3703,3876,4006,4123,4245,4364,4454,4552,4671,4807,4905,5023,5125,5251,5384,5489,5587,5667,5760,5853,5967,6051,6136,6237,6317,6401,6502,6601,6696,6796,6883,6988,7090,7195,7312,7392,7494", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "1557,1670,1777,1891,1991,2086,2198,2342,2464,2613,2697,2797,2886,2980,3094,3212,3317,3442,3562,3698,3871,4001,4118,4240,4359,4449,4547,4666,4802,4900,5018,5120,5246,5379,5484,5582,5662,5755,5848,5962,6046,6131,6232,6312,6396,6497,6596,6691,6791,6878,6983,7085,7190,7307,7387,7489,7588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ebc026bb76e99bbb3a1041a14632c8ff\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,971,1055,1125,1199,1271,1342,1420,1487", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,966,1050,1120,1194,1266,1337,1415,1482,1602"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,924,1004,1099,1198,1280,1357,7593,7682,7764,7845,7929,7999,8073,8145,8317,8395,8462", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,73,71,70,77,66,119", "endOffsets": "919,999,1094,1193,1275,1352,1441,7677,7759,7840,7924,7994,8068,8140,8211,8390,8457,8577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b57ffc025acb7e960dd010855e1c62ee\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8582,8672", "endColumns": "89,86", "endOffsets": "8667,8754"}}]}]}