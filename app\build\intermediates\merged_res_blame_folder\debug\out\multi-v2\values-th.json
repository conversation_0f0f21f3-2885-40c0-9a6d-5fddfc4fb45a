{"logs": [{"outputFile": "com.example.my_music_001.app-mergeDebugResources-48:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3dfcadad40a732775724e376d406a3c9\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8572,8669", "endColumns": "96,94", "endOffsets": "8664,8759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f812aff50628458f92d3d65ed6b25c25\\transformed\\material3-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4534,4620,4703,4808,4888,4975,5074,5176,5270,5374,5460,5561,5659,5762,5879,5959,6069", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4529,4615,4698,4803,4883,4970,5069,5171,5265,5369,5455,5556,5654,5757,5874,5954,6064,6170"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1439,1552,1664,1777,1889,1988,2081,2191,2321,2445,2586,2672,2772,2863,2961,3079,3195,3300,3427,3551,3679,3831,3954,4072,4196,4317,4409,4508,4620,4753,4849,4967,5074,5200,5334,5444,5542,5623,5717,5811,5918,6004,6087,6192,6272,6359,6458,6560,6654,6758,6844,6945,7043,7146,7263,7343,7453", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "1547,1659,1772,1884,1983,2076,2186,2316,2440,2581,2667,2767,2858,2956,3074,3190,3295,3422,3546,3674,3826,3949,4067,4191,4312,4404,4503,4615,4748,4844,4962,5069,5195,5329,5439,5537,5618,5712,5806,5913,5999,6082,6187,6267,6354,6453,6555,6649,6753,6839,6940,7038,7141,7258,7338,7448,7554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c207a12670627327d0104d8324c8bc3f\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,8203", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,8299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c02159d97b58df1ac6d5f03a0b5b0bd8\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,906,983,1080,1181,1269,1354,7559,7645,7728,7814,7903,7976,8051,8127,8304,8382,8449", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "901,978,1075,1176,1264,1349,1434,7640,7723,7809,7898,7971,8046,8122,8198,8377,8444,8567"}}]}]}