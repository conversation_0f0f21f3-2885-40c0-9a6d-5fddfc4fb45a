package com.example.my_music_001
//状态栏
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class NotificationReceiver : BroadcastReceiver() {
    companion object {
        const val ACTION_PLAY_PAUSE = "com.example.my_music_001.ACTION_PLAY_PAUSE"
        const val ACTION_PREVIOUS = "com.example.my_music_001.ACTION_PREVIOUS"
        const val ACTION_NEXT = "com.example.my_music_001.ACTION_NEXT"
        const val EXTRA_IS_PLAYING = "is_playing"
        
        // 用于广播的Action
        const val BROADCAST_ACTION = "com.example.my_music_001.NOTIFICATION_ACTION"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("NotificationReceiver", "收到通知按钮点击: ${intent.action}")
        
        // 创建广播Intent
        val broadcastIntent = Intent(BROADCAST_ACTION).apply {
            // 设置包名，确保只有本应用能接收
            `package` = context.packageName
            // 传递原始action
            putExtra(Intent.EXTRA_INTENT, intent.action)
            // 传递额外的播放状态
            if (intent.action == ACTION_PLAY_PAUSE) {
                putExtra(EXTRA_IS_PLAYING, intent.getBooleanExtra(EXTRA_IS_PLAYING, false))
            }
        }
        
        // 发送广播
        context.sendBroadcast(broadcastIntent)
        Log.d("NotificationReceiver", "已发送广播: ${intent.action}")
    }
}
