package com.example.my_music_001
//ztl
import android.app.Activity
import android.view.WindowInsetsController
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.consumeAllChanges
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.abs
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.draw.alpha
import androidx.compose.animation.core.*
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer

/**
 * 从右侧滑入的抽屉式对话框
 */
@Composable
fun RightSwipeDialog(
    onDismiss: () -> Unit,
    isDarkMode: Boolean = false,
    progress: Float = 1f // 显示进度，0-1之间
) {
    // 获取屏幕宽度和高度
    val screenWidth = LocalConfiguration.current.screenWidthDp.dp
    val screenHeight = LocalConfiguration.current.screenHeightDp.dp
    
    // 创建动画状态
    val animatedProgress = remember { Animatable(progress) }
    
    // 获取窗口
    val window = (LocalContext.current as Activity).window
    
    // 当进度改变时触发动画
    LaunchedEffect(progress) {
        // 设置状态栏始终透明
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        
        // 根据弹窗背景颜色调整状态栏文字颜色
        if (animatedProgress.value > 0.2f) {  // 只有当弹窗显示时才改变状态栏文字颜色
            if (isDarkMode) {
                // 黑色背景时保持默认状态栏文字颜色
                window.insetsController?.setSystemBarsAppearance(
                    0,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                )
            } else {
                // 白色背景时设置状态栏文字为黑色
                window.insetsController?.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                )
            }
        } else {
            // 弹窗隐藏时恢复默认状态栏文字颜色
            window.insetsController?.setSystemBarsAppearance(
                0,
                WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
            )
        }
        // 如果进度在20%到100%之间，保持当前进度
        if (progress > 0.2f && progress < 1f) {
            animatedProgress.snapTo(progress)
        }
        // 如果进度超过100%，执行展开动画
        else if (progress >= 1f) {
            animatedProgress.animateTo(
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                )
            )
        }
        // 如果进度小于20%，重置进度
        else {
            animatedProgress.snapTo(progress)
        }
    }
    
    // 计算位移 - 从屏幕右侧向左滑入
    val offsetX = screenWidth.value * (1 - animatedProgress.value)
    
    // 使用两层Box的结构，外层完全透明，只处理点击事件，内层为抽屉内容
    Box(
        modifier = Modifier
            .fillMaxSize() // 填充整个屏幕
            // 移除背景色和alpha属性，使外层Box完全透明
            // 点击背景区域关闭弹窗
            .pointerInput(Unit) {
                detectTapGestures { 
                    onDismiss()
                }
            }
    ) {
        // 内层抽屉内容
        Box(
            modifier = Modifier
                .fillMaxHeight() // 高度填满屏幕
                .width(screenWidth * 1f) // 宽度为屏幕的100%
                .align(Alignment.CenterEnd) // 右对齐
                .offset(x = offsetX.dp) // 根据进度从右侧滑入
                .background(if (!isDarkMode) Color.White else Color.Black) // 背景颜色根据暗黑模式变化
                // 添加左滑关闭功能
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        change.consumeAllChanges()
                        // 检测水平方向的滑动
                        if (dragAmount.x > 5 && abs(dragAmount.y) < abs(dragAmount.x)) {
                            // 左滑，关闭弹窗
                            onDismiss()
                        }
                    }
                }
                // 防止点击事件穿透到背景
                .pointerInput(Unit) {
                    detectTapGestures { }
                },
            contentAlignment = Alignment.Center // 内容居中
        ) {
            // 添加内容
            Text(
                "Hello 希 望 鱼",
                style = TextStyle(
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (!isDarkMode) Color.Black else Color.White
                )
            )
        }
    }
} 