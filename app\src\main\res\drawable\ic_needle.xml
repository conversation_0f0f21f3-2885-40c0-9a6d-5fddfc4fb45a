<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="160dp"
    android:viewportWidth="120"
    android:viewportHeight="160">

    <!-- 长条底部根部的圆角正方形 - 与弯折部分垂直90度，逆时针旋转7度，增加圆角 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M77.5,122.2 Q77.0,121.2 78.0,121.2 L85.2,117.6 Q86.2,117.1 87.2,117.6 Q88.2,118.1 88.7,119.1 L92.6,127.1 Q93.1,128.1 92.6,129.1 Q92.1,130.1 91.1,130.1 L83.9,133.6 Q82.9,134.1 81.9,133.6 Q80.9,133.1 80.4,132.1 L77.0,124.1 Q76.5,123.1 77.0,122.1 Q77.5,121.1 77.5,122.2 Z" />

    <!-- 正方形内部左侧黑色小长条 - 逆时针旋转5度 -->
    <path
        android:fillColor="#808080"
        android:pathData="M79.1,124.6 L80.1,124.1 L82.3,129.4 L81.3,129.9 Z" />

    <!-- 正方形内部右侧黑色小长条 - 变长 -->
    <path
        android:fillColor="#808080"
        android:pathData="M87.1,121.6 L88.1,121.1 L90.3,126.4 L89.3,126.9 Z" />

    <!-- 唱针整体：使用单一路径实现平滑弯折，避免灰色线条 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M57,12 L63,12 L63,65 C63,76 68,86 73,96 L85.46,122 L79.46,122 L67,96 C62,86 57,76 57,65 L57,12 Z" />

    <!-- 唱针支点 - 适度增大半径 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M60,21 A9,9 0 1,1 60,3 A9,9 0 1,1 60,21 Z" />
    
    <!-- 唱针支点灰色圆形 - 适度增大半径 -->
    <path
        android:fillColor="#D5D5D5"
        android:pathData="M60,17 A5,5 0 1,1 60,7 A5,5 0 1,1 60,17 Z" />
    
    <!-- 唱针支点灰色边框 - 适度增大半径 -->
    <path
        android:strokeColor="#808080"
        android:strokeWidth="0.2"
        android:pathData="M60,21 A9,9 0 1,1 60,3 A9,9 0 1,1 60,21 Z" />

</vector>